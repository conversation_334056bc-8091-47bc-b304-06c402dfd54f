"""
Database models for Solar Display application

Defines Pydantic models and SQLite table schemas for:
- Solar production data from Deye inverter
- Weather data from Open-Meteo API  

All models are optimized for time-series data storage and analysis
for the Villieria, Pretoria solar installation.
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field

# =================== DATABASE MODELS ===================

class SolarProductionData(BaseModel):
    """Solar production data from Deye inverter API"""
    id: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    station_id: str = Field(..., description="Deye station identifier")
    total_power: float = Field(..., description="Total system power (kW)")
    daily_energy: float = Field(..., description="Daily energy production (kWh)")
    inverter_temp: Optional[float] = Field(None, description="Inverter temperature (°C)")
    dc_voltage: Optional[float] = Field(None, description="DC input voltage (V)")
    ac_voltage: Optional[float] = Field(None, description="AC output voltage (V)")
    efficiency: Optional[float] = Field(None, description="System efficiency (%)")
    grid_frequency: Optional[float] = Field(None, description="Grid frequency (Hz)")
    battery_soc: Optional[float] = Field(None, description="Battery state of charge (%)")
    battery_power: Optional[float] = Field(None, description="Battery charge/discharge (kW)")
    grid_power: Optional[float] = Field(None, description="Grid import/export (kW)")
    consumption_power: Optional[float] = Field(None, description="Current consumption (kW)")
    irradiate_intensity: Optional[float] = Field(None, description="Solar irradiation (W/m²)")
    error_code: Optional[str] = Field(None, description="Any error codes")
    raw_data: Optional[str] = Field(None, description="Complete JSON from Deye API")

class WeatherData(BaseModel):
    """Weather data from Open-Meteo API for Pretoria coordinates"""
    id: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    temperature: float = Field(..., description="Air temperature (°C)")
    humidity: float = Field(..., description="Relative humidity (%)")
    solar_radiation: float = Field(..., description="Solar irradiance (W/m²)")
    cloud_cover: float = Field(..., description="Cloud coverage (%)")
    wind_speed: float = Field(..., description="Wind speed (m/s)")
    pressure: Optional[float] = Field(None, description="Atmospheric pressure (hPa)")
    weather_code: Optional[int] = Field(None, description="WMO weather code")
    source: str = Field(default="open-meteo", description="Weather data source")

# =================== API RESPONSE MODELS ===================

class SolarDataResponse(BaseModel):
    """Response model for solar production data API"""
    success: bool
    data: List[SolarProductionData]
    total_records: int
    timestamp: datetime = Field(default_factory=datetime.now)

class WeatherDataResponse(BaseModel):
    """Response model for weather data API"""
    success: bool
    data: List[WeatherData]
    total_records: int
    timestamp: datetime = Field(default_factory=datetime.now)
