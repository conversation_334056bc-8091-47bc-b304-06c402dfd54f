from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import structlog
from fastapi import Request
import time
import asyncio

from src.routers import solar, websockets, auth
from src.config import settings
from src.database import initialize_database

app = FastAPI(
    title=settings.api_title,
    description="Real-time solar inverter monitoring - Simple Deye API Interface",
    version=settings.api_version,
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins.split(','),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(GZipMiddleware, minimum_size=1000)

# Structured logging
logger = structlog.get_logger()

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = (time.time() - start_time) * 1000
    logger.info(
        "request_processed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=f"{process_time:.2f}ms"
    )
    
    return response

# Routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(solar.router, prefix="/api/solar", tags=["solar-data"])
app.include_router(websockets.router, prefix="/ws", tags=["websockets"])

@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    logger.info("Initializing Solar Display Application...")
    
    try:
        # Initialize database
        await initialize_database()
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error(f"Startup initialization failed: {e}")
        # Don't crash the app - it can still function in monitoring mode

@app.get("/")
def read_root():
    return {
        "message": "Welcome to the Solar Display API",
        "description": "Simple solar energy monitoring interface for Deye inverters",
        "location": "Villieria, Pretoria, South Africa",
        "features": [
            "Real-time solar production monitoring",
            "Current power generation display",
            "Battery status monitoring", 
            "Grid power monitoring"
        ],
        "docs": "/api/docs"
    }

@app.get("/api/health")
async def health_check():
    """Health check endpoint for monitoring"""
    from src.database import get_database_stats
    
    try:
        # Check database health
        db_stats = await get_database_stats()
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "database": {
                "connected": True,
                "stats": db_stats
            },
            "services": {
                "deye_api": settings.is_deye_config_valid(),
                "weather_api": True,  # Open-Meteo doesn't require auth
                "data_collection": settings.enable_automated_collection
            }
        }
        
    except Exception as e:
        return {
            "status": "degraded",
            "timestamp": time.time(),
            "error": str(e),
            "database": {"connected": False},
            "services": {
                "deye_api": settings.is_deye_config_valid(),
                "weather_api": True,
                "data_collection": False
            }
        }
