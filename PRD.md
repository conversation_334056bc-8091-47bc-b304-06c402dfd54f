# Solar Display - Simple Monitoring Interface

## Overview
A clean, simple web interface for monitoring Deye solar inverter data in real-time. This application provides exactly what's needed: **current values from the Deye API** displayed in a modern, responsive interface.

## Features

### Core Monitoring Interface
- **Real-time KPI Cards**
  - Current power generation (kW)
  - Daily energy production (kWh)
  - Battery state of charge (%)
  - Battery charge/discharge status
  - Grid import/export monitoring
  - House consumption tracking

- **Interactive Historical Charts**
  - 24-hour, 7-day, 30-day views
  - Generation vs consumption comparison
  - Plotly.js interactive visualizations

- **Live Updates**
  - WebSocket streaming for real-time data
  - Automatic fallback to API polling
  - Connection status indicators

### Technical Stack
- **Backend**: FastAPI (Python)
- **Frontend**: HTML/CSS/JavaScript
- **Database**: SQLite
- **Charts**: Plotly.js
- **API**: Deye Cloud API (read-only)

### Design Principles
- **Simple & Clean**: No complex processing or analytics
- **Fast & Reliable**: Minimal dependencies
- **Mobile Responsive**: Works on all devices
- **Read-Only Safe**: Never modifies inverter settings
- **Easy Setup**: Single configuration file

## API Integration

### Deye Solar Cloud API
- Read-only access to inverter data
- 15-minute data collection intervals
- Automatic error handling and retries
- Secure authentication with stored credentials

### Data Collected
- Solar generation power and energy
- Battery status and power flow
- Grid connection status
- System efficiency metrics
- Basic weather data (optional)

## Architecture

### Simple Three-Tier Design
```
Frontend (Browser) ← WebSocket/HTTP → Backend (FastAPI) ← HTTP → Deye API
                                           ↓
                                      SQLite Database
```

### Directory Structure
```
Solar Display/
├── src/                    # Backend source code
│   ├── main.py            # FastAPI application
│   ├── config.py          # Configuration management
│   ├── database/          # Database models & migrations
│   ├── routers/           # API endpoints
│   └── services/          # Business logic
├── frontend/              # Web interface
│   ├── index.html         # Main dashboard
│   ├── css/styles.css     # Styling
│   └── js/                # JavaScript modules
└── requirements.txt       # Python dependencies
```

## Configuration

### Environment Variables (.env)
```bash
# Deye API Credentials
DEYE_EMAIL=<EMAIL>
DEYE_PASSWORD=your_password

# Application Settings  
ACCESS_CODE=1234
API_PORT=8000
DATABASE_PATH=solar_display.db

# Optional Features
ENABLE_WEATHER_DATA=false
ENABLE_DATA_COLLECTION=true
```

## Installation & Usage

### Quick Start
1. **Clone & Install**
   ```bash
   git clone <repository>
   cd solar-display
   pip install -r requirements.txt
   ```

2. **Configure**
   ```bash
   cp .env.example .env
   # Edit .env with your Deye credentials
   ```

3. **Run**
   ```bash
   python src/main.py
   ```

4. **Access**
   - Open http://localhost:8000
   - Enter access code (default: 1234)
   - Monitor your solar system!

### Production Deployment
- Use uvicorn with --host 0.0.0.0 for network access
- Set up reverse proxy (nginx) for HTTPS
- Configure systemd service for auto-start
- Regular database backups recommended

## Security & Safety

### Read-Only Design
- Only uses GET endpoints from Deye API
- Never modifies inverter configuration
- No write access to hardware systems
- Safe to run continuously

### Authentication
- Simple access code protection
- No user accounts or complex auth
- Configurable access restrictions
- Session-based login

### Data Privacy
- All data stored locally (SQLite)
- No cloud services required
- No external analytics tracking
- Complete data ownership

## Monitoring Capabilities

### Real-Time Display
- **Generation Power**: Current solar output
- **Daily Energy**: Cumulative daily production
- **Battery Status**: SOC, charge/discharge rate
- **Grid Power**: Import/export status
- **Consumption**: Current house usage

### Historical Analysis
- Interactive charts with zoom/pan
- Multiple time range options
- Export data functionality
- Trend visualization

### System Health
- Connection status monitoring
- Data freshness indicators
- Simple health checks
- Error reporting

## Benefits

### For Users
- **Simple Setup**: Works out of the box
- **Clean Interface**: Easy to understand
- **Mobile Friendly**: Access from any device
- **Always Available**: Local hosting, no cloud dependency
- **Cost Effective**: No subscription fees

### For Developers
- **Clean Codebase**: Well-organized, documented
- **Modern Stack**: FastAPI, WebSocket, modern JS
- **Extensible**: Easy to add features
- **Maintainable**: Minimal dependencies
- **Testable**: Clear separation of concerns

## What This Application Does NOT Include

To maintain simplicity and reliability:
- ❌ Machine learning or AI predictions
- ❌ Complex analytics or forecasting
- ❌ Heavy data processing
- ❌ Cloud services or external APIs (except Deye)
- ❌ User management systems
- ❌ Mobile apps or complex UIs

## Future Enhancement Options

If desired, the architecture supports adding:
- Multi-station monitoring
- Email/SMS alerts
- Data export features
- Advanced reporting
- Mobile applications
- Integration with home automation

---

**Result**: A focused, reliable solar monitoring interface that shows exactly what's needed - current values from your Deye inverter in a clean, modern web interface.
