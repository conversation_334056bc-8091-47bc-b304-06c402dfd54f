#!/usr/bin/env python3
"""
Debug Solar Generation Pattern

Test the mathematical formula for solar generation to ensure
it peaks at the correct time (noon = 12:00 PM).
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta

def test_solar_pattern():
    """Test solar generation pattern for 24 hours"""
    print("🔍 Testing Solar Generation Pattern")
    print("=" * 50)
    
    # Test each hour from 0 to 23
    results = []
    
    for hour in range(24):
        # Current solar generation formula
        if 6 <= hour <= 18:
            hours_from_sunrise = hour - 6  # 0 to 12 hours
            time_factor = np.sin(np.pi * hours_from_sunrise / 12)
            generation = 15.0 * time_factor  # Base 15kW system
        else:
            time_factor = 0.0
            generation = 0.0
        
        results.append({
            'hour': hour,
            'time': f"{hour:02d}:00",
            'hours_from_sunrise': hour - 6 if 6 <= hour <= 18 else 0,
            'time_factor': time_factor,
            'generation_kw': generation
        })
        
        print(f"{hour:02d}:00 | Hours from sunrise: {hour-6:2d} | Factor: {time_factor:.3f} | Generation: {generation:.2f} kW")
    
    # Find peak
    df = pd.DataFrame(results)
    peak_idx = df['generation_kw'].idxmax()
    peak_data = df.iloc[peak_idx]
    
    print("\n" + "=" * 50)
    print(f"🌟 PEAK GENERATION:")
    print(f"   Time: {peak_data['time']}")
    print(f"   Generation: {peak_data['generation_kw']:.2f} kW")
    print(f"   Hours from sunrise: {peak_data['hours_from_sunrise']}")
    
    # Check if peak is at noon (12:00)
    if peak_data['hour'] == 12:
        print("   ✅ Peak is correctly at noon (12:00)")
    else:
        print(f"   ❌ Peak should be at 12:00, but is at {peak_data['time']}")
        
        # Calculate when sine peaks
        # sin(π * x / 12) peaks when x = 6
        # So hour = 6 + 6 = 12 ✓
        print(f"   📐 Math check: sin(π * x / 12) peaks when x = 6")
        print(f"   📐 So hour = 6 + x = 6 + 6 = 12 ✓")
    
    return df

def find_actual_peak_in_data():
    """Analyze the actual data to see what's happening"""
    print("\n🔍 Analyzing Current Data Issue")
    print("=" * 50)
    
    # The issue might be in weather data modifying the pure solar pattern
    # Let's check what the weather data looks like
    
    sample_weather_effects = {
        6: {'temp': 10, 'cloud': 20, 'radiation': 200},   # Early morning
        8: {'temp': 15, 'cloud': 30, 'radiation': 600},   # Morning  
        10: {'temp': 20, 'cloud': 40, 'radiation': 900},  # Late morning
        12: {'temp': 25, 'cloud': 50, 'radiation': 1000}, # Noon
        14: {'temp': 28, 'cloud': 60, 'radiation': 800},  # Afternoon
        16: {'temp': 25, 'cloud': 50, 'radiation': 400},  # Late afternoon
    }
    
    print("Sample weather effects on generation:")
    for hour, weather in sample_weather_effects.items():
        # Base solar pattern
        if 6 <= hour <= 18:
            hours_from_sunrise = hour - 6
            time_factor = np.sin(np.pi * hours_from_sunrise / 12)
            base_generation = 15.0 * time_factor
            
            # Weather effects (simplified calculation)
            temp = weather['temp']
            cloud_cover = weather['cloud'] / 100.0
            solar_radiation = weather['radiation']
            
            cloud_factor = 1.0 - (cloud_cover * 0.7)
            temp_factor = 1.0 - max(0, (temp - 25) * 0.004)
            radiation_factor = min(1.0, solar_radiation / 1000.0)
            
            final_generation = base_generation * cloud_factor * temp_factor * radiation_factor
            
            print(f"{hour:02d}:00 | Base: {base_generation:.2f} | Cloud: {cloud_factor:.2f} | Temp: {temp_factor:.2f} | Rad: {radiation_factor:.2f} | Final: {final_generation:.2f}")
        else:
            print(f"{hour:02d}:00 | (Night time)")

if __name__ == "__main__":
    # Test the basic solar pattern
    df = test_solar_pattern()
    
    # Analyze weather effects
    find_actual_peak_in_data()
