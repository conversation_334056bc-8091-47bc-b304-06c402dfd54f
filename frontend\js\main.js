// Main Dashboard Application

class SolarDashboard {
    constructor() {
        this.currentStation = null;
        this.currentStationId = null;
        this.chartData = [];
        this.isInitialized = false;
        this.updateInterval = null;
        this.lastDataUpdate = null;
        
        // Chart configuration
        this.chartConfig = {
            displayModeBar: false,
            responsive: true,
            displaylogo: false
        };

        // Initialize the application
        this.init();
    }

    async init() {
        console.log('Initializing Solar Dashboard...');
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Check for stored credentials
        const storedCode = solarAPI.getStoredAccessCode();
        if (storedCode) {
            solarAPI.setAccessCode(storedCode);
            await this.attemptAutoLogin();
        }
        
        this.isInitialized = true;
    }

    setupEventListeners() {
        // Login form
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.handleLogout();
        });

        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.refreshData();
        });

        // Chart time range buttons
        document.getElementById('chart24h').addEventListener('click', () => {
            this.changeChartTimeRange('24h');
        });
        document.getElementById('chart7d').addEventListener('click', () => {
            this.changeChartTimeRange('7d');
        });
        document.getElementById('chart30d').addEventListener('click', () => {
            this.changeChartTimeRange('30d');
        });

        // Error toast close button
        document.getElementById('closeError').addEventListener('click', () => {
            this.hideError();
        });

        // WebSocket callbacks
        solarWebSocket.setCallbacks(
            (data) => this.handleRealtimeData(data),
            (status, message) => this.updateConnectionStatus(status, message)
        );
    }

    async attemptAutoLogin() {
        try {
            this.showLoading('Connecting...');
            
            // Test API connection
            const healthOk = await solarAPI.healthCheck();
            if (!healthOk) {
                throw new Error('API server is not available');
            }

            // Try to fetch stations to validate credentials
            await this.loadDashboard();
            
        } catch (error) {
            console.error('Auto-login failed:', error);
            this.showLogin();
        } finally {
            this.hideLoading();
        }
    }

    async handleLogin() {
        const accessCode = document.getElementById('accessCode').value.trim();
        const errorDiv = document.getElementById('loginError');
        
        if (!accessCode) {
            this.showLoginError('Please enter an access code');
            return;
        }

        try {
            this.showLoading('Authenticating...');
            errorDiv.textContent = '';
            
            // Attempt login
            await solarAPI.login(accessCode);
            
            // Load dashboard
            await this.loadDashboard();
            
        } catch (error) {
            console.error('Login failed:', error);
            this.showLoginError(error.message);
        } finally {
            this.hideLoading();
        }
    }

    async loadDashboard() {
        try {
            // Fetch stations
            const stations = await solarAPI.getStations();
            
            if (!stations || stations.length === 0) {
                throw new Error('No solar stations found');
            }

            // Use the first station
            this.currentStation = stations[0];
            this.currentStationId = this.currentStation.id;
            
            // Show dashboard
            this.showDashboard();
            
            // Load initial data
            await this.loadStationData();
            
            // Set up real-time updates
            this.setupRealtimeUpdates();
            
            // Set up periodic refresh fallback
            this.setupPeriodicRefresh();
            
        } catch (error) {
            console.error('Failed to load dashboard:', error);
            this.showError('Failed to load dashboard: ' + error.message);
            this.showLogin();
        }
    }

    async loadStationData() {
        try {
            // Load real-time data
            const realtimeData = await solarAPI.getStationLatest(this.currentStationId);
            this.updateKPICards(realtimeData);
            
            // Load historical data for chart
            const historyData = await solarAPI.getStationHistory(this.currentStationId, 7);
            this.updateChart(historyData);
            
            // Update system status
            this.updateSystemStatus(realtimeData);
            
            this.lastDataUpdate = new Date();
            this.updateLastUpdateTime();
            
        } catch (error) {
            console.error('Failed to load station data:', error);
            this.showError('Failed to load station data: ' + error.message);
        }
    }

    setupRealtimeUpdates() {
        if (this.currentStationId) {
            solarWebSocket.connect(this.currentStationId);
        }
    }

    setupPeriodicRefresh() {
        // Refresh data every 60 seconds as fallback
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.updateInterval = setInterval(() => {
            if (!solarWebSocket.isWebSocketConnected()) {
                console.log('WebSocket not connected, using API polling');
                this.refreshData();
            }
        }, 60000);
    }

    handleRealtimeData(data) {
        console.log('Received real-time data:', data);
        this.updateKPICards(data);
        this.updateSystemStatus(data);
        this.lastDataUpdate = new Date();
        this.updateLastUpdateTime();
    }

    updateKPICards(data) {
        // Generation Power
        document.getElementById('generationPower').textContent = `${(data.generationPower || 0).toFixed(1)} kW`;
        document.getElementById('generationDaily').textContent = `Daily: ${(data.generationValue || 0).toFixed(1)} kWh`;
        
        // Battery
        const batterySoc = data.batterySoc || 0;
        const batteryPower = data.batteryPower || 0;
        
        document.getElementById('batterySoc').textContent = `${batterySoc.toFixed(0)}%`;
        document.getElementById('batteryPower').textContent = this.formatBatteryPower(batteryPower);
        document.getElementById('batteryFill').style.width = `${batterySoc}%`;
        
        // Grid Power
        const gridPower = data.gridPower || 0;
        document.getElementById('gridPower').textContent = `${Math.abs(gridPower).toFixed(1)} kW`;
        document.getElementById('gridStatus').textContent = gridPower < 0 ? 'Exporting' : 'Importing';
        
        // Consumption
        document.getElementById('consumptionPower').textContent = `${(data.consumptionPower || 0).toFixed(1)} kW`;
        document.getElementById('consumptionDaily').textContent = `Daily: ${(data.consumptionValue || 0).toFixed(1)} kWh`;
    }

    formatBatteryPower(power) {
        if (power > 0.1) {
            return `⬆ ${power.toFixed(1)} kW`;
        } else if (power < -0.1) {
            return `⬇ ${Math.abs(power).toFixed(1)} kW`;
        } else {
            return '⏸ Idle';
        }
    }

    updateChart(historyData) {
        if (!historyData || !historyData.data || historyData.data.length === 0) {
            console.log('No historical data available for chart');
            return;
        }

        const dates = historyData.data.map(item => item.date);
        const generation = historyData.data.map(item => item.generation_value || 0);
        const consumption = historyData.data.map(item => item.consumption_value || 0);

        const traces = [
            {
                x: dates,
                y: generation,
                type: 'scatter',
                mode: 'lines+markers',
                name: 'Generation',
                line: { color: '#FFD23F', width: 3 },
                marker: { size: 6 }
            },
            {
                x: dates,
                y: consumption,
                type: 'scatter',
                mode: 'lines+markers',
                name: 'Consumption',
                line: { color: '#2E86AB', width: 3 },
                marker: { size: 6 }
            }
        ];

        const layout = {
            title: false,
            xaxis: {
                title: 'Date',
                gridcolor: '#4a4a4a',
                color: '#b0b0b0'
            },
            yaxis: {
                title: 'Energy (kWh)',
                gridcolor: '#4a4a4a',
                color: '#b0b0b0'
            },
            plot_bgcolor: 'transparent',
            paper_bgcolor: 'transparent',
            font: { color: '#b0b0b0' },
            legend: {
                orientation: 'h',
                y: 1.1,
                x: 0.5,
                xanchor: 'center'
            },
            margin: { l: 50, r: 50, t: 20, b: 50 }
        };

        Plotly.newPlot('powerChart', traces, layout, this.chartConfig);
    }

    updateSystemStatus(data) {
        const statusElement = document.getElementById('systemStatus');
        const detailsElement = document.getElementById('statusDetails');
        
        // Simple status logic based on data
        let status = 'normal';
        let statusText = 'Normal';
        let details = 'All systems operating normally.';
        
        const generation = data.generationPower || 0;
        const batterySoc = data.batterySoc || 0;
        
        if (batterySoc < 20) {
            status = 'warning';
            statusText = 'Warning';
            details = 'Battery level is low (< 20%).';
        } else if (generation < 0.1 && new Date().getHours() > 8 && new Date().getHours() < 18) {
            status = 'warning';
            statusText = 'Warning';
            details = 'Low solar generation during daylight hours.';
        }
        
        statusElement.textContent = statusText;
        statusElement.className = `status-indicator ${status}`;
        detailsElement.innerHTML = `<p>${details}</p>`;
    }

    updateConnectionStatus(status, message) {
        const statusElement = document.getElementById('connectionStatus');
        const iconElement = statusElement.querySelector('i');
        const textElement = statusElement.querySelector('span');
        
        statusElement.className = `connection-status ${status}`;
        textElement.textContent = message;
        
        switch (status) {
            case 'connected':
                iconElement.className = 'fas fa-circle';
                break;
            case 'disconnected':
            case 'error':
                iconElement.className = 'fas fa-circle';
                break;
            case 'reconnecting':
                iconElement.className = 'fas fa-spinner fa-spin';
                break;
        }
    }

    updateLastUpdateTime() {
        if (this.lastDataUpdate) {
            document.getElementById('lastUpdate').textContent = this.lastDataUpdate.toLocaleTimeString();
        }
    }

    changeChartTimeRange(range) {
        // Update active button
        document.querySelectorAll('.chart-controls .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(`chart${range}`).classList.add('active');
        
        // Load data for the selected range
        const days = range === '24h' ? 1 : range === '7d' ? 7 : 30;
        this.loadChartData(days);
    }

    async loadChartData(days) {
        try {
            const historyData = await solarAPI.getStationHistory(this.currentStationId, days);
            this.updateChart(historyData);
        } catch (error) {
            console.error('Failed to load chart data:', error);
        }
    }

    async refreshData() {
        console.log('Refreshing data...');
        await this.loadStationData();
    }

    handleLogout() {
        solarWebSocket.disconnect();
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        solarAPI.clearCredentials();
        this.showLogin();
    }

    // UI Helper Methods
    showLogin() {
        document.getElementById('loginModal').classList.add('active');
        document.getElementById('dashboard').classList.add('hidden');
        document.getElementById('accessCode').value = '';
        document.getElementById('accessCode').focus();
    }

    showDashboard() {
        document.getElementById('loginModal').classList.remove('active');
        document.getElementById('dashboard').classList.remove('hidden');
    }

    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        overlay.querySelector('p').textContent = message;
        overlay.classList.remove('hidden');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.add('hidden');
    }

    showLoginError(message) {
        document.getElementById('loginError').textContent = message;
    }

    showError(message) {
        const toast = document.getElementById('errorToast');
        document.getElementById('errorMessage').textContent = message;
        toast.classList.remove('hidden');
        
        // Auto-hide after 5 seconds
        setTimeout(() => this.hideError(), 5000);
    }

    hideError() {
        document.getElementById('errorToast').classList.add('hidden');
    }
}

// Initialize the dashboard when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new SolarDashboard();
});
