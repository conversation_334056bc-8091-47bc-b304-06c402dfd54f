# Plotly Dash Real-Time Dashboard Framework for Solar Monitoring

## Executive Summary

Plotly Dash provides the ideal framework for creating interactive, real-time solar monitoring dashboards with sophisticated data visualization capabilities. This analysis covers <PERSON>'s real-time features, component architecture, callback systems, and implementation patterns specifically designed for solar energy monitoring applications with WebSocket integration and responsive design.

## Why Plotly Dash for Solar Dashboards

### Real-Time Visualization Strengths
- **Interactive Components**: Rich set of UI components for solar data display
- **Real-Time Updates**: Callback system for live data streaming
- **WebSocket Integration**: Native support for real-time data connections
- **Responsive Design**: Mobile-first approach for monitoring on any device
- **Plotly Integration**: Advanced charting capabilities for time-series data

### Solar Monitoring Specific Benefits
- **Time-Series Visualization**: Perfect for energy production/consumption graphs
- **Multi-Panel Dashboards**: Comprehensive system overview in single interface
- **Interactive Visualizations**: Drill-down capabilities for detailed data exploration
- **Alert Systems**: Visual indicators for system status and anomalies
- **Export Capabilities**: Data export and report generation features

### Enterprise Features
- **Performance**: Optimized for large datasets and continuous updates
- **Customization**: Extensive theming and styling options
- **Deployment**: Easy deployment with various hosting options
- **Integration**: Seamless integration with FastAPI backend

## Core Dashboard Architecture

### Application Structure
```
dashboard/
├── app.py                     # Main Dash application
├── components/                # Reusable dashboard components
│   ├── solar_cards.py        # KPI cards for key metrics
│   ├── time_series_charts.py # Energy production/consumption charts
│   ├── status_indicators.py  # System status and alerts
│   ├── battery_gauge.py      # Battery SOC and flow visualization
│   └── weather_display.py    # Weather and environmental data
├── layouts/                   # Page layouts and structure
│   ├── main_dashboard.py     # Primary dashboard layout
│   ├── reports_page.py       # Data reports and summaries
│   └── settings_page.py      # Configuration and settings
├── callbacks/                 # Dash callback functions
│   ├── real_time_updates.py  # Live data update callbacks
│   ├── user_interactions.py  # User interaction handlers
│   └── data_exports.py       # Export and download callbacks
├── utils/                     # Utility functions
│   ├── data_processing.py    # Data transformation utilities
│   ├── styling.py           # CSS and styling utilities
│   └── websocket_client.py  # WebSocket client for real-time data
└── assets/                   # Static assets
    ├── custom.css           # Custom CSS styles
    ├── solar_theme.css      # Solar-specific theme
    └── images/              # Icons and images
```

### Main Application Setup
```python
import dash
from dash import html, dcc, Input, Output, State, callback
import dash_bootstrap_components as dbc
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import pandas as pd
import asyncio
import websockets
import json

# Initialize Dash app with Bootstrap theme
app = dash.Dash(
    __name__,
    external_stylesheets=[
        dbc.themes.BOOTSTRAP,
        "https://use.fontawesome.com/releases/v6.0.0/css/all.css"
    ],
    suppress_callback_exceptions=True,
    update_title=None  # Prevent title updates during callbacks
)

app.title = "Solar Monitor Dashboard"
server = app.server  # For deployment

# Global configuration
CONFIG = {
    'update_interval': 30000,  # 30 seconds
    'websocket_url': 'ws://localhost:8000/ws/solar',
    'api_base_url': 'http://localhost:8000/api',
    'theme_colors': {
        'primary': '#2E86AB',
        'secondary': '#A23B72',
        'success': '#F18F01',
        'warning': '#C73E1D',
        'solar_yellow': '#FFD23F',
        'battery_green': '#06D6A0'
    }
}

# Layout structure
app.layout = dbc.Container([
    dcc.Store(id='station-data-store'),
    dcc.Store(id='websocket-data-store'),
    dcc.Interval(
        id='interval-component',
        interval=CONFIG['update_interval'],
        n_intervals=0
    ),
    
    # Header
    dbc.Row([
        dbc.Col([
            html.H1([
                html.I(className="fas fa-solar-panel me-2"),
                "Solar Energy Monitor"
            ], className="text-center mb-4")
        ])
    ]),
    
    # Main dashboard content
    html.Div(id='dashboard-content'),
    
    # WebSocket connection status
    dbc.Toast(
        id="connection-toast",
        header="Connection Status",
        is_open=False,
        duration=4000,
        style={"position": "fixed", "top": 66, "right": 10, "width": 350}
    )
], fluid=True)
```

## Real-Time Components

### WebSocket Client Integration
```python
import websockets
import asyncio
import json
from threading import Thread
import queue

class DashWebSocketClient:
    def __init__(self, station_id: str, data_queue: queue.Queue):
        self.station_id = station_id
        self.data_queue = data_queue
        self.websocket = None
        self.connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        
    async def connect(self):
        """Connect to WebSocket server"""
        uri = f"{CONFIG['websocket_url']}/{self.station_id}"
        
        try:
            self.websocket = await websockets.connect(uri)
            self.connected = True
            self.reconnect_attempts = 0
            print(f"Connected to WebSocket: {uri}")
            
            # Listen for messages
            await self.listen_for_data()
            
        except Exception as e:
            print(f"WebSocket connection failed: {e}")
            await self.handle_reconnect()
    
    async def listen_for_data(self):
        """Listen for incoming WebSocket data"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                self.data_queue.put(data)
                
        except websockets.exceptions.ConnectionClosed:
            print("WebSocket connection closed")
            self.connected = False
            await self.handle_reconnect()
            
        except Exception as e:
            print(f"Error receiving WebSocket data: {e}")
    
    async def handle_reconnect(self):
        """Handle WebSocket reconnection with exponential backoff"""
        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            wait_time = min(30, 2 ** self.reconnect_attempts)
            
            print(f"Reconnecting in {wait_time} seconds (attempt {self.reconnect_attempts})")
            await asyncio.sleep(wait_time)
            await self.connect()
        else:
            print("Max reconnection attempts reached")
    
    def run_in_thread(self):
        """Run WebSocket client in separate thread"""
        asyncio.run(self.connect())

# Global WebSocket data queue
websocket_data_queue = queue.Queue()

def start_websocket_client(station_id: str):
    """Start WebSocket client in background thread"""
    client = DashWebSocketClient(station_id, websocket_data_queue)
    thread = Thread(target=client.run_in_thread, daemon=True)
    thread.start()
    return client
```

### Real-Time KPI Cards
```python
def create_solar_kpi_cards(data: dict) -> list:
    """Create KPI cards for solar system metrics"""
    
    cards = []
    
    # Generation Power Card
    cards.append(
        dbc.Card([
            dbc.CardBody([
                html.H4([
                    html.I(className="fas fa-solar-panel text-warning me-2"),
                    "Generation"
                ]),
                html.H2(
                    f"{data.get('generation_power', 0):.1f} kW",
                    className="text-primary"
                ),
                html.P([
                    html.Small([
                        html.I(className="fas fa-arrow-up text-success me-1"),
                        f"Today: {data.get('daily_generation', 0):.1f} kWh"
                    ])
                ])
            ])
        ], className="h-100")
    )
    
    # Battery SOC Card
    battery_soc = data.get('battery_soc', 0)
    battery_color = 'success' if battery_soc > 50 else 'warning' if battery_soc > 20 else 'danger'
    
    cards.append(
        dbc.Card([
            dbc.CardBody([
                html.H4([
                    html.I(className="fas fa-battery-three-quarters text-success me-2"),
                    "Battery"
                ]),
                html.H2(
                    f"{battery_soc:.0f}%",
                    className=f"text-{battery_color}"
                ),
                dbc.Progress(
                    value=battery_soc,
                    color=battery_color,
                    className="mb-2"
                ),
                html.P([
                    html.Small([
                        _get_battery_status_icon(data.get('battery_power', 0)),
                        f" {abs(data.get('battery_power', 0)):.1f} kW"
                    ])
                ])
            ])
        ], className="h-100")
    )
    
    # Grid Power Card
    grid_power = data.get('grid_power', 0)
    grid_status = 'export' if grid_power < 0 else 'import'
    grid_color = 'success' if grid_power < 0 else 'primary'
    
    cards.append(
        dbc.Card([
            dbc.CardBody([
                html.H4([
                    html.I(className="fas fa-plug text-info me-2"),
                    "Grid"
                ]),
                html.H2(
                    f"{abs(grid_power):.1f} kW",
                    className=f"text-{grid_color}"
                ),
                html.P([
                    html.Small([
                        html.I(className=f"fas fa-arrow-{'down' if grid_power < 0 else 'up'} me-1"),
                        grid_status.title()
                    ])
                ])
            ])
        ], className="h-100")
    )
    
    # Consumption Card
    cards.append(
        dbc.Card([
            dbc.CardBody([
                html.H4([
                    html.I(className="fas fa-home text-secondary me-2"),
                    "Consumption"
                ]),
                html.H2(
                    f"{data.get('consumption_power', 0):.1f} kW",
                    className="text-secondary"
                ),
                html.P([
                    html.Small([
                        html.I(className="fas fa-clock me-1"),
                        f"Today: {data.get('daily_consumption', 0):.1f} kWh"
                    ])
                ])
            ])
        ], className="h-100")
    )
    
    return cards

def _get_battery_status_icon(battery_power: float) -> html.I:
    """Get battery status icon based on power flow"""
    if battery_power > 0.1:
        return html.I(className="fas fa-arrow-up text-success me-1")  # Charging
    elif battery_power < -0.1:
        return html.I(className="fas fa-arrow-down text-warning me-1")  # Discharging
    else:
        return html.I(className="fas fa-pause text-muted me-1")  # Idle
```

### Real-Time Charts
```python
def create_real_time_power_chart(historical_data: pd.DataFrame) -> go.Figure:
    """Create real-time power flow chart"""
    
    fig = go.Figure()
    
    # Generation power
    fig.add_trace(go.Scatter(
        x=historical_data.index,
        y=historical_data['generation_power'],
        mode='lines',
        name='Solar Generation',
        line=dict(color=CONFIG['theme_colors']['solar_yellow'], width=3),
        fill='tonexty' if len(fig.data) > 0 else 'tozeroy',
        fillcolor=f"rgba(255, 210, 63, 0.2)"
    ))
    
    # Consumption power
    fig.add_trace(go.Scatter(
        x=historical_data.index,
        y=historical_data['consumption_power'],
        mode='lines',
        name='Consumption',
        line=dict(color=CONFIG['theme_colors']['primary'], width=2)
    ))
    
    # Battery power (with positive/negative areas)
    battery_positive = historical_data['battery_power'].clip(lower=0)
    battery_negative = historical_data['battery_power'].clip(upper=0)
    
    fig.add_trace(go.Scatter(
        x=historical_data.index,
        y=battery_positive,
        mode='lines',
        name='Battery Charging',
        line=dict(color=CONFIG['theme_colors']['battery_green'], width=2),
        fill='tozeroy',
        fillcolor=f"rgba(6, 214, 160, 0.3)"
    ))
    
    fig.add_trace(go.Scatter(
        x=historical_data.index,
        y=battery_negative,
        mode='lines',
        name='Battery Discharging',
        line=dict(color=CONFIG['theme_colors']['warning'], width=2),
        fill='tozeroy',
        fillcolor=f"rgba(241, 143, 1, 0.3)"
    ))
    
    # Grid power
    fig.add_trace(go.Scatter(
        x=historical_data.index,
        y=historical_data['grid_power'],
        mode='lines',
        name='Grid Power',
        line=dict(color=CONFIG['theme_colors']['secondary'], width=2, dash='dash')
    ))
    
    # Layout configuration
    fig.update_layout(
        title={
            'text': 'Real-Time Power Flow',
            'x': 0.5,
            'xanchor': 'center'
        },
        xaxis_title='Time',
        yaxis_title='Power (kW)',
        hovermode='x unified',
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        ),
        margin=dict(l=0, r=0, t=50, b=0),
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font=dict(size=12),
        height=400
    )
    
    # Update axes
    fig.update_xaxes(
        gridcolor='rgba(128,128,128,0.2)',
        showgrid=True,
        rangeslider_visible=False
    )
    fig.update_yaxes(
        gridcolor='rgba(128,128,128,0.2)',
        showgrid=True,
        zeroline=True,
        zerolinecolor='rgba(128,128,128,0.4)'
    )
    
    return fig

def create_energy_balance_chart(daily_data: dict) -> go.Figure:
    """Create daily energy balance chart"""
    
    categories = ['Generation', 'Consumption', 'Battery Charge', 'Battery Discharge', 'Grid Import', 'Grid Export']
    values = [
        daily_data.get('generation_value', 0),
        daily_data.get('consumption_value', 0),
        max(0, daily_data.get('battery_charge', 0)),
        abs(min(0, daily_data.get('battery_discharge', 0))),
        max(0, daily_data.get('grid_import', 0)),
        abs(min(0, daily_data.get('grid_export', 0)))
    ]
    
    colors = [
        CONFIG['theme_colors']['solar_yellow'],
        CONFIG['theme_colors']['primary'],
        CONFIG['theme_colors']['battery_green'],
        CONFIG['theme_colors']['warning'],
        CONFIG['theme_colors']['secondary'],
        CONFIG['theme_colors']['success']
    ]
    
    fig = go.Figure(data=[
        go.Bar(
            x=categories,
            y=values,
            marker_color=colors,
            text=[f'{v:.1f} kWh' for v in values],
            textposition='auto'
        )
    ])
    
    fig.update_layout(
        title={
            'text': 'Daily Energy Balance',
            'x': 0.5,
            'xanchor': 'center'
        },
        yaxis_title='Energy (kWh)',
        showlegend=False,
        margin=dict(l=0, r=0, t=50, b=0),
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        height=300
    )
    
    return fig
```

### Advanced Gauge Components
```python
def create_battery_gauge(soc: float, power: float) -> go.Figure:
    """Create battery state of charge gauge"""
    
    # Determine gauge color based on SOC
    if soc >= 70:
        gauge_color = CONFIG['theme_colors']['battery_green']
    elif soc >= 30:
        gauge_color = CONFIG['theme_colors']['solar_yellow']
    else:
        gauge_color = CONFIG['theme_colors']['warning']
    
    fig = go.Figure(go.Indicator(
        mode="gauge+number+delta",
        value=soc,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': "Battery SOC", 'font': {'size': 20}},
        delta={'reference': 50, 'position': "top"},
        gauge={
            'axis': {'range': [None, 100], 'tickwidth': 1, 'tickcolor': "darkblue"},
            'bar': {'color': gauge_color},
            'bgcolor': "white",
            'borderwidth': 2,
            'bordercolor': "gray",
            'steps': [
                {'range': [0, 20], 'color': 'rgba(255, 0, 0, 0.3)'},
                {'range': [20, 50], 'color': 'rgba(255, 165, 0, 0.3)'},
                {'range': [50, 80], 'color': 'rgba(255, 255, 0, 0.3)'},
                {'range': [80, 100], 'color': 'rgba(0, 255, 0, 0.3)'}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 90
            }
        }
    ))
    
    # Add power flow annotation
    power_text = f"{'Charging' if power > 0 else 'Discharging' if power < 0 else 'Idle'}: {abs(power):.1f} kW"
    
    fig.add_annotation(
        x=0.5, y=0.1,
        text=power_text,
        showarrow=False,
        font=dict(size=14, color=gauge_color)
    )
    
    fig.update_layout(
        height=300,
        margin=dict(l=20, r=20, t=40, b=20),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)'
    )
    
    return fig

def create_system_status_indicator(status_data: dict) -> dbc.Alert:
    """Create system status indicator"""
    
    overall_status = status_data.get('overall_status', 'unknown')
    last_update = status_data.get('last_update', 'Never')
    alerts = status_data.get('alerts', [])
    
    # Determine alert color and icon
    if overall_status == 'normal':
        color = 'success'
        icon = 'check-circle'
        title = 'System Normal'
    elif overall_status == 'warning':
        color = 'warning'
        icon = 'exclamation-triangle'
        title = 'System Warning'
    elif overall_status == 'error':
        color = 'danger'
        icon = 'times-circle'
        title = 'System Error'
    else:
        color = 'secondary'
        icon = 'question-circle'
        title = 'Status Unknown'
    
    content = [
        html.H5([
            html.I(className=f"fas fa-{icon} me-2"),
            title
        ]),
        html.P(f"Last Update: {last_update}", className="mb-2")
    ]
    
    # Add alerts if any
    if alerts:
        content.append(html.Hr())
        content.append(html.P("Active Alerts:", className="fw-bold mb-1"))
        for alert in alerts[:3]:  # Show max 3 alerts
            content.append(html.Li(alert, className="small"))
        
        if len(alerts) > 3:
            content.append(html.P(f"... and {len(alerts) - 3} more", className="small text-muted"))
    
    return dbc.Alert(content, color=color, className="mb-3")
```

## Callback System for Real-Time Updates

### Main Data Update Callbacks
```python
@app.callback(
    [Output('station-data-store', 'data'),
     Output('connection-toast', 'is_open'),
     Output('connection-toast', 'children'),
     Output('connection-toast', 'header')],
    [Input('interval-component', 'n_intervals')],
    [State('station-data-store', 'data')]
)
def update_station_data(n_intervals, current_data):
    """Main callback to update station data"""
    
    try:
        # Check for WebSocket data first
        websocket_data = None
        try:
            while not websocket_data_queue.empty():
                websocket_data = websocket_data_queue.get_nowait()
        except queue.Empty:
            pass
        
        if websocket_data:
            # Use WebSocket data if available
            station_data = websocket_data.get('data', {})
            connection_status = {
                'is_open': False,
                'children': "Real-time connection active",
                'header': "WebSocket Connected"
            }
        else:
            # Fallback to API polling
            station_data = fetch_station_data_from_api()
            connection_status = {
                'is_open': True,
                'children': "Using API polling (WebSocket unavailable)",
                'header': "Connection Status"
            }
        
        return station_data, *connection_status.values()
        
    except Exception as e:
        error_data = current_data or {}
        return error_data, True, f"Error updating data: {str(e)}", "Connection Error"

@app.callback(
    [Output('kpi-cards-container', 'children'),
     Output('real-time-chart', 'figure'),
     Output('energy-balance-chart', 'figure'),
     Output('battery-gauge', 'figure'),
     Output('system-status', 'children')],
    [Input('station-data-store', 'data')],
    prevent_initial_call=False
)
def update_dashboard_components(station_data):
    """Update all dashboard components with new data"""
    
    if not station_data:
        # Return empty/default components
        return [], {}, {}, {}, []
    
    try:
        # Update KPI cards
        kpi_cards = [
            dbc.Col(card, width=12, lg=3, className="mb-3")
            for card in create_solar_kpi_cards(station_data)
        ]
        
        # Update charts
        historical_df = pd.DataFrame(station_data.get('historical_data', []))
        if not historical_df.empty:
            historical_df['timestamp'] = pd.to_datetime(historical_df['timestamp'])
            historical_df.set_index('timestamp', inplace=True)
            
            real_time_chart = create_real_time_power_chart(historical_df)
        else:
            real_time_chart = create_empty_chart("No historical data available")
        
        # Energy balance chart
        daily_data = station_data.get('daily_summary', {})
        energy_balance_chart = create_energy_balance_chart(daily_data)
        
        # Battery gauge
        battery_soc = station_data.get('battery_soc', 0)
        battery_power = station_data.get('battery_power', 0)
        battery_gauge = create_battery_gauge(battery_soc, battery_power)
        
        # System status
        status_data = station_data.get('status', {})
        system_status = create_system_status_indicator(status_data)
        
        return kpi_cards, real_time_chart, energy_balance_chart, battery_gauge, system_status
        
    except Exception as e:
        # Return error components
        error_message = f"Error updating dashboard: {str(e)}"
        return [], create_error_chart(error_message), {}, {}, [html.P(error_message, className="text-danger")]

def create_empty_chart(message: str) -> go.Figure:
    """Create empty chart with message"""
    fig = go.Figure()
    fig.add_annotation(
        x=0.5, y=0.5,
        text=message,
        showarrow=False,
        font=dict(size=16)
    )
    fig.update_layout(
        xaxis=dict(visible=False),
        yaxis=dict(visible=False),
        plot_bgcolor='rgba(0,0,0,0)',
        height=400
    )
    return fig
```

### Interactive Callbacks
```python
@app.callback(
    Output('time-range-chart', 'figure'),
    [Input('time-range-picker', 'start_date'),
     Input('time-range-picker', 'end_date'),
     Input('chart-type-selector', 'value')],
    [State('station-data-store', 'data')]
)
def update_time_range_chart(start_date, end_date, chart_type, station_data):
    """Update chart based on user-selected time range"""
    
    if not station_data or not start_date or not end_date:
        return create_empty_chart("Select date range to view data")
    
    try:
        # Filter data by date range
        historical_data = pd.DataFrame(station_data.get('historical_data', []))
        if historical_data.empty:
            return create_empty_chart("No data available for selected range")
        
        historical_data['timestamp'] = pd.to_datetime(historical_data['timestamp'])
        mask = (historical_data['timestamp'] >= start_date) & (historical_data['timestamp'] <= end_date)
        filtered_data = historical_data.loc[mask]
        
        if filtered_data.empty:
            return create_empty_chart("No data in selected range")
        
        # Create chart based on type
        if chart_type == 'power':
            return create_real_time_power_chart(filtered_data.set_index('timestamp'))
        elif chart_type == 'energy':
            return create_energy_cumulative_chart(filtered_data.set_index('timestamp'))
        elif chart_type == 'efficiency':
            return create_efficiency_chart(filtered_data.set_index('timestamp'))
        else:
            return create_empty_chart("Invalid chart type selected")
            
    except Exception as e:
        return create_error_chart(f"Error creating chart: {str(e)}")

@app.callback(
    Output('download-data', 'data'),
    [Input('export-button', 'n_clicks')],
    [State('station-data-store', 'data'),
     State('time-range-picker', 'start_date'),
     State('time-range-picker', 'end_date')],
    prevent_initial_call=True
)
def export_data(n_clicks, station_data, start_date, end_date):
    """Export station data to CSV"""
    
    if not n_clicks or not station_data:
        return dash.no_update
    
    try:
        # Prepare data for export
        historical_data = pd.DataFrame(station_data.get('historical_data', []))
        if not historical_data.empty:
            historical_data['timestamp'] = pd.to_datetime(historical_data['timestamp'])
            
            # Filter by date range if provided
            if start_date and end_date:
                mask = (historical_data['timestamp'] >= start_date) & (historical_data['timestamp'] <= end_date)
                historical_data = historical_data.loc[mask]
            
            # Create filename with timestamp
            filename = f"solar_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            return dcc.send_data_frame(historical_data.to_csv, filename, index=False)
        else:
            return dash.no_update
            
    except Exception as e:
        print(f"Export error: {e}")
        return dash.no_update
```

## Advanced Dashboard Features

### Multi-Station Management
```python
@app.callback(
    [Output('station-selector', 'options'),
     Output('station-selector', 'value')],
    [Input('app-load', 'children')]
)
def load_station_options(app_load):
    """Load available stations for selection"""
    
    try:
        # Fetch stations from API
        stations = fetch_stations_from_api()
        
        options = [
            {'label': f"{station['name']} ({station['capacity']} kW)", 
             'value': station['id']}
            for station in stations
        ]
        
        # Select first station by default
        default_value = options[0]['value'] if options else None
        
        return options, default_value
        
    except Exception as e:
        return [], None

@app.callback(
    Output('station-comparison-chart', 'figure'),
    [Input('compare-stations-button', 'n_clicks')],
    [State('station-comparison-selector', 'value')]
)
def create_station_comparison(n_clicks, selected_stations):
    """Create comparison chart for multiple stations"""
    
    if not n_clicks or not selected_stations or len(selected_stations) < 2:
        return create_empty_chart("Select 2 or more stations to compare")
    
    try:
        fig = go.Figure()
        
        for station_id in selected_stations:
            station_data = fetch_station_data_from_api(station_id)
            historical_data = pd.DataFrame(station_data.get('historical_data', []))
            
            if not historical_data.empty:
                historical_data['timestamp'] = pd.to_datetime(historical_data['timestamp'])
                
                fig.add_trace(go.Scatter(
                    x=historical_data['timestamp'],
                    y=historical_data['generation_power'],
                    mode='lines',
                    name=f"Station {station_id}",
                    line=dict(width=2)
                ))
        
        fig.update_layout(
            title="Station Comparison - Generation Power",
            xaxis_title="Time",
            yaxis_title="Power (kW)",
            hovermode='x unified',
            height=400
        )
        
        return fig
        
    except Exception as e:
        return create_error_chart(f"Error creating comparison: {str(e)}")
```

### Alert System Integration
```python
@app.callback(
    [Output('alert-modal', 'is_open'),
     Output('alert-modal-body', 'children')],
    [Input('alert-notification', 'n_clicks'),
     Input('close-alert-modal', 'n_clicks')],
    [State('alert-modal', 'is_open'),
     State('station-data-store', 'data')]
)
def handle_alert_modal(alert_clicks, close_clicks, is_open, station_data):
    """Handle alert notification modal"""
    
    ctx = dash.callback_context
    if not ctx.triggered:
        return False, []
    
    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
    
    if trigger_id == 'alert-notification':
        # Show alert details
        alerts = station_data.get('alerts', []) if station_data else []
        
        if not alerts:
            return False, []
        
        alert_content = []
        for alert in alerts:
            alert_content.append(
                dbc.Alert([
                    html.H6(alert.get('title', 'System Alert'), className="alert-heading"),
                    html.P(alert.get('description', 'No description available')),
                    html.Hr(),
                    html.P([
                        html.Small([
                            f"Time: {alert.get('timestamp', 'Unknown')} | ",
                            f"Severity: {alert.get('severity', 'Unknown')}"
                        ])
                    ], className="mb-0")
                ], color=alert.get('color', 'warning'))
            )
        
        return True, alert_content
    
    elif trigger_id == 'close-alert-modal':
        return False, []
    
    return is_open, []
```

## Responsive Design and Mobile Optimization

### Mobile-First Layout
```python
def create_mobile_optimized_layout():
    """Create responsive layout optimized for mobile devices"""
    
    return dbc.Container([
        # Mobile header with hamburger menu
        dbc.NavbarSimple(
            brand="Solar Monitor",
            brand_href="#",
            color="primary",
            dark=True,
            className="mb-4 d-lg-none"  # Only show on mobile
        ),
        
        # Responsive grid system
        dbc.Row([
            # Sidebar (hidden on mobile)
            dbc.Col([
                create_sidebar_content()
            ], width=12, lg=3, className="d-none d-lg-block"),
            
            # Main content
            dbc.Col([
                # Mobile KPI cards (stack vertically)
                dbc.Row([
                    dbc.Col(card, width=12, md=6, lg=3, className="mb-3")
                    for card in create_mobile_kpi_cards()
                ], className="mb-4"),
                
                # Charts (responsive sizing)
                dbc.Row([
                    dbc.Col([
                        dcc.Graph(
                            id='mobile-chart',
                            config={
                                'displayModeBar': False,  # Hide toolbar on mobile
                                'responsive': True
                            },
                            style={'height': '300px'}  # Fixed height for mobile
                        )
                    ], width=12)
                ])
            ], width=12, lg=9)
        ])
    ], fluid=True)

# Custom CSS for mobile optimization
mobile_css = """
@media (max-width: 768px) {
    .card-body {
        padding: 1rem 0.75rem;
    }
    
    .card-title {
        font-size: 1rem;
    }
    
    .display-4 {
        font-size: 1.5rem;
    }
    
    .chart-container {
        height: 250px !important;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 0.5rem;
    }
}
"""
```

## Performance Optimization

### Data Caching and Optimization
```python
from functools import lru_cache
import time

# Cache for expensive computations
@lru_cache(maxsize=128)
def cached_data_processing(data_hash: str, processing_type: str):
    """Cache expensive data processing operations"""
    # Implement caching logic
    pass

# Optimize callback performance
@app.callback(
    Output('optimized-chart', 'figure'),
    [Input('data-store', 'data')],
    prevent_initial_call=True
)
def optimized_chart_update(data):
    """Optimized chart update with data sampling"""
    
    if not data:
        return dash.no_update
    
    try:
        df = pd.DataFrame(data)
        
        # Sample data for large datasets
        if len(df) > 1000:
            # Keep every nth point for performance
            sample_rate = len(df) // 500
            df = df.iloc[::sample_rate]
        
        # Use efficient plotting methods
        fig = px.line(
            df, x='timestamp', y='generation_power',
            title='Generation Power (Optimized)'
        )
        
        # Optimize layout for performance
        fig.update_layout(
            autosize=True,
            margin=dict(l=0, r=0, t=30, b=0),
            showlegend=False
        )
        
        return fig
        
    except Exception as e:
        return create_error_chart(f"Error: {str(e)}")

# Background data processing
import threading

class BackgroundDataProcessor:
    def __init__(self):
        self.processing_queue = queue.Queue()
        self.results = {}
        self.worker_thread = threading.Thread(target=self._process_data, daemon=True)
        self.worker_thread.start()
    
    def _process_data(self):
        """Background thread for data processing"""
        while True:
            try:
                task = self.processing_queue.get(timeout=1)
                
                # Process data
                result = self._expensive_calculation(task['data'])
                self.results[task['id']] = result
                
                self.processing_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"Background processing error: {e}")
    
    def _expensive_calculation(self, data):
        """Simulate expensive calculation"""
        time.sleep(0.1)  # Simulate processing time
        return {"processed": True, "timestamp": time.time()}

# Global background processor
background_processor = BackgroundDataProcessor()
```

## Deployment and Production Considerations

### Docker Configuration
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m dashuser && chown -R dashuser:dashuser /app
USER dashuser

EXPOSE 8050

CMD ["python", "app.py"]
```

### Production Settings
```python
# Production configuration
PRODUCTION_CONFIG = {
    'debug': False,
    'host': '0.0.0.0',
    'port': 8050,
    'threaded': True,
    'processes': 1,
    'update_interval': 30000,  # 30 seconds
    'cache_timeout': 300,      # 5 minutes
    'max_workers': 4,
    'websocket_timeout': 60,
    'compression': True
}

if __name__ == '__main__':
    # Initialize WebSocket client
    start_websocket_client('default_station')
    
    # Run app
    app.run_server(
        debug=PRODUCTION_CONFIG['debug'],
        host=PRODUCTION_CONFIG['host'],
        port=PRODUCTION_CONFIG['port'],
        threaded=PRODUCTION_CONFIG['threaded']
    )
```

## Conclusion

Plotly Dash provides an exceptional framework for creating sophisticated, real-time solar monitoring dashboards. Key advantages for this project:

**Real-Time Capabilities:**
- **WebSocket Integration**: Seamless real-time data streaming
- **Callback System**: Efficient reactive updates without page refreshes
- **Background Processing**: Non-blocking data processing and updates
- **Live Charts**: Dynamic visualizations with smooth updates

**User Experience:**
- **Interactive Components**: Rich UI elements for solar data exploration
- **Responsive Design**: Mobile-optimized layouts for field monitoring
- **Alert System**: Visual notifications for system issues
- **Export Features**: Data download and reporting capabilities

**Technical Excellence:**
- **Performance Optimization**: Efficient handling of large time-series datasets
- **Scalable Architecture**: Support for multiple stations and concurrent users
- **Production Ready**: Docker deployment and enterprise-grade features
- **Extensibility**: Easy integration with FastAPI backend and data services

**Solar-Specific Features:**
- **Energy Flow Visualization**: Comprehensive power flow diagrams
- **Battery Management**: Advanced SOC gauges and efficiency metrics
- **Performance Monitoring**: Trend analysis and efficiency tracking
- **System Alerts**: Real-time system status notifications

This dashboard framework provides the ideal foundation for creating a professional solar monitoring interface that combines real-time data visualization with comprehensive monitoring and user-friendly design.
