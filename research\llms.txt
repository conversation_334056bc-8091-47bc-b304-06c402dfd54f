# LLMs.txt - Research Index for Solar Display Project

This file serves as an index for the research markdown files created for the Solar Display project. It outlines the content of each file to help the development process.

---

### **File: `01-deye-api-analysis.md`**

**Purpose:** Comprehensive analysis of the Deye Solar Inverter Cloud API. This document is the primary reference for all backend interactions with the Deye platform.

**Contents:**
- **API Architecture:** Base URL, versioning, and protocol information.
- **Authentication:** Detailed flow for obtaining and using Bearer tokens, including `APP_ID`, `APP_SECRET`, and SHA-256 password hashing.
- **Core Read-Only Endpoints:** A curated list of essential `GET` endpoints for station management, device operations, and configuration reading, ensuring the application remains read-only.
- **Key Data Points:** Breakdown of available real-time and historical metrics, such as generation power, battery SOC, grid power, and energy values (kWh).
- **Data Granularity:** Explanation of temporal granularity options (frame, daily, monthly, yearly).
- **Rate Limits & Best Practices:** Recommended polling intervals and notes on API constraints.
- **Error Handling:** Standard HTTP status codes and the structure of API error responses.
- **Security:** Critical safety measures for a read-only application, including token storage and HTTPS enforcement.
- **Implementation Recommendations:** Python code snippets for authentication management and data retrieval strategies.

---

### **File: `02-fastapi-backend-architecture.md`**

**Purpose:** Architectural blueprint for the Python backend using the FastAPI framework. This file guides the development of the server-side application.

**Contents:**
- **Why FastAPI:** Justification for choosing FastAPI, highlighting performance, async support, and automatic documentation.
- **Core Architecture:** Proposed project structure, including directories for models, services, routers, and dependencies.
- **Data Models (Pydantic):** Definitions for `SolarStation`, `RealTimeData`, and `HistoricalData` models to ensure type safety and data validation.
- **Service Layer:** Implementation details for the `DeyeAPIService` (interfacing with the Deye API) and `SolarDataService` (business logic and data processing).
- **WebSocket Real-Time Streaming:** A complete guide to implementing real-time data updates, including a `WebSocketManager` and endpoint examples.
- **API Endpoints:** Structure for RESTful API endpoints for retrieving station data, historical trends, and analytics.
- **Background Tasks:** Strategy for scheduled data collection, historical data synchronization, and periodic analytics updates.
- **Configuration Management:** Using Pydantic's `BaseSettings` for environment-based configuration.
- **Testing Strategy:** Framework for unit and integration tests using `pytest` and `TestClient`.
- **Production Deployment:** Docker and Docker Compose configurations for containerization and local development.

---



### **File: `04-plotly-dash-dashboard.md`**

**Purpose:** Design and implementation guide for the interactive, real-time frontend dashboard using Plotly Dash.

**Contents:**
- **Why Plotly Dash:** Reasons for selecting Dash, focusing on its real-time visualization capabilities and Python-based nature.
- **Core Dashboard Architecture:** Proposed file structure for the Dash application, including components, layouts, and callbacks.
- **Real-Time Components:**
    - **WebSocket Integration:** A `DashWebSocketClient` class to connect to the FastAPI backend for live data.
    - **KPI Cards, Charts, and Gauges:** Code examples for creating key visual components like power flow charts, energy balance bars, and battery SOC gauges.
- **Callback System:**
    - **Main Data Updates:** Callbacks for handling data from WebSockets and API polling.
    - **Interactive Callbacks:** Callbacks for user interactions like date range selection and data export.
- **Advanced Features:**
    - **Multi-Station Management:** UI/UX for selecting and comparing data from multiple solar stations.
    - **Alert System:** Modals and notifications for displaying system alerts.
- **Responsive Design:** Strategies for creating a mobile-first, responsive layout using `dash-bootstrap-components`.
- **Performance Optimization:** Techniques for client-side performance, including data sampling and background processing.
- **Deployment:** Docker configuration for deploying the Dash application.
