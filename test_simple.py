#!/usr/bin/env python3
"""
Simple Solar Display Application Test

Tests the basic functionality of the simplified solar monitoring interface:
- API connectivity
- Basic data collection  
- Frontend accessibility
"""

import asyncio
import httpx
import time
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000"
ACCESS_CODE = "1234"

class SimpleTests:
    """Basic functionality tests for the solar monitoring interface"""
    
    def __init__(self):
        self.success_count = 0
        self.total_tests = 0
    
    async def run_all_tests(self):
        """Run all basic functionality tests"""
        print("🔋 Solar Display - Basic Functionality Tests")
        print("=" * 50)
        
        # Test 1: API Health Check
        await self.test_api_health()
        
        # Test 2: Authentication
        await self.test_authentication()
        
        # Test 3: Solar Data Endpoints
        await self.test_solar_endpoints()
        
        # Test 4: Frontend Accessibility
        await self.test_frontend_access()
        
        # Summary
        self.show_summary()
    
    async def test_api_health(self):
        """Test API server health"""
        print("\n1️⃣ Testing API Health...")
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{BASE_URL}/api/health")
                
                if response.status_code == 200:
                    data = response.json()
                    self.log_success(f"API Health: {data.get('status', 'unknown')}")
                    
                    # Check services
                    services = data.get('services', {})
                    for service, status in services.items():
                        self.log_info(f"   {service}: {'✅' if status else '❌'}")
                else:
                    self.log_error(f"Health check failed: {response.status_code}")
                    
        except Exception as e:
            self.log_error(f"Health check error: {e}")
    
    async def test_authentication(self):
        """Test basic authentication"""
        print("\n2️⃣ Testing Authentication...")
        
        try:
            async with httpx.AsyncClient() as client:
                # Test login
                response = await client.post(
                    f"{BASE_URL}/api/auth/token",
                    json={"code": ACCESS_CODE}
                )
                
                if response.status_code == 200:
                    data = response.json()
                    self.log_success(f"Authentication successful: {data.get('message', 'OK')}")
                else:
                    self.log_error(f"Authentication failed: {response.status_code}")
                    
        except Exception as e:
            self.log_error(f"Authentication error: {e}")
    
    async def test_solar_endpoints(self):
        """Test solar data endpoints"""
        print("\n3️⃣ Testing Solar Data Endpoints...")
        
        try:
            async with httpx.AsyncClient() as client:
                # Set auth header
                headers = {"X-Access-Code": ACCESS_CODE}
                
                # Test stations endpoint
                response = await client.get(f"{BASE_URL}/api/solar/stations", headers=headers)
                if response.status_code == 200:
                    stations = response.json()
                    self.log_success(f"Stations: {len(stations)} found")
                    
                    if stations:
                        station_id = stations[0]['id']
                        
                        # Test latest data
                        response = await client.get(f"{BASE_URL}/api/solar/stations/{station_id}/latest", headers=headers)
                        if response.status_code == 200:
                            data = response.json()
                            self.log_success(f"Latest data: {data.get('generationPower', 0):.1f} kW")
                        else:
                            self.log_error(f"Latest data failed: {response.status_code}")
                        
                        # Test history
                        response = await client.get(f"{BASE_URL}/api/solar/stations/{station_id}/history?days=7", headers=headers)
                        if response.status_code == 200:
                            history = response.json()
                            data_points = len(history.get('data', []))
                            self.log_success(f"History data: {data_points} points")
                        else:
                            self.log_error(f"History data failed: {response.status_code}")
                else:
                    self.log_error(f"Stations endpoint failed: {response.status_code}")
                    
        except Exception as e:
            self.log_error(f"Solar endpoints error: {e}")
    
    async def test_frontend_access(self):
        """Test frontend accessibility"""
        print("\n4️⃣ Testing Frontend Access...")
        
        try:
            async with httpx.AsyncClient() as client:
                # Test main page
                response = await client.get(BASE_URL)
                
                if response.status_code == 200:
                    content = response.text
                    if "Solar Display" in content:
                        self.log_success("Frontend accessible and contains expected content")
                    else:
                        self.log_error("Frontend accessible but missing expected content")
                else:
                    self.log_error(f"Frontend access failed: {response.status_code}")
                    
        except Exception as e:
            self.log_error(f"Frontend access error: {e}")
    
    def log_success(self, message):
        """Log successful test"""
        print(f"   ✅ {message}")
        self.success_count += 1
        self.total_tests += 1
    
    def log_error(self, message):
        """Log failed test"""
        print(f"   ❌ {message}")
        self.total_tests += 1
    
    def log_info(self, message):
        """Log informational message"""
        print(f"   ℹ️  {message}")
    
    def show_summary(self):
        """Show test summary"""
        print("\n" + "=" * 50)
        print(f"📊 Test Summary: {self.success_count}/{self.total_tests} tests passed")
        
        if self.success_count == self.total_tests:
            print("🎉 All tests passed! Solar Display is working correctly.")
        else:
            print("⚠️  Some tests failed. Check the configuration and server status.")
        
        print("\n💡 Next Steps:")
        print("   1. Open http://localhost:8000 in your browser")
        print(f"   2. Login with access code: {ACCESS_CODE}")
        print("   3. Monitor your solar system!")

async def main():
    """Main test execution"""
    print("Starting Solar Display Basic Tests...")
    print(f"Testing against: {BASE_URL}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Wait a moment for server to be ready
    await asyncio.sleep(1)
    
    # Run tests
    tester = SimpleTests()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
